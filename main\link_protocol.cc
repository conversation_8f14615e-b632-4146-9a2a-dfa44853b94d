#include <stdio.h>
#include <vector>
#include <string>
#include <mutex>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_event.h"
#include "nvs_flash.h"
#include "esp_log.h"
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "services/gap/ble_svc_gap.h"
#include "host/ble_uuid.h"
#include "host/ble_gap.h"
#include "host/ble_hs_adv.h"
#include "sdkconfig.h"
#include <cJSON.h>

extern uint8_t ble_addr_type;
static int scan_device_count = 0;
static const int MAX_SCAN_DEVICES = 10;   // 最大扫描设备数量
static const int SCAN_DURATION_MS = 3000; // 扫描持续时间：3秒

// Advertising related constants
#define LINK_PROTOCOL_MANUFACTURER_ID_V1 0x01
#define LINK_PROTOCOL_MANUFACTURER_ID_V2 0x02
#define LINK_PROTOCOL_MANUFACTURER_ID_V3 0x03

// Advertising state
static bool g_advertising_active = false;
static bool g_ext_advertising_active = false;
static uint8_t g_ext_adv_instance = 0;

// Forward declarations
extern "C" void ble_app_scan(void);

// --- BLE scan result aggregation for MCP tool ---
struct BleDeviceInfo
{
    std::string name;
    std::string addr;
    int rssi;
};
static std::vector<BleDeviceInfo> g_ble_scan_results;
static SemaphoreHandle_t g_ble_scan_done_sem = nullptr;
static std::mutex g_ble_results_mutex;

static inline std::string fmt_addr_str(const ble_addr_t *addr)
{
    char buf[18];
    const uint8_t *a = addr->val;
    snprintf(buf, sizeof(buf), "%02X:%02X:%02X:%02X:%02X:%02X", a[5], a[4], a[3], a[2], a[1], a[0]);
    return std::string(buf);
}

// Start scan and block until complete, return JSON string with results
std::string ble_scan_start_and_wait_json()
{
    {
        std::lock_guard<std::mutex> lk(g_ble_results_mutex);
        g_ble_scan_results.clear();
    }
    if (g_ble_scan_done_sem == nullptr)
    {
        g_ble_scan_done_sem = xSemaphoreCreateBinary();
    }
    else
    {
        xQueueReset(g_ble_scan_done_sem);
    }
    ble_app_scan();
    // Wait until DISC_COMPLETE event or timeout slightly longer than scan duration
    TickType_t wait_ticks = pdMS_TO_TICKS(SCAN_DURATION_MS + 1000);
    xSemaphoreTake(g_ble_scan_done_sem, wait_ticks);

    // Build JSON
    cJSON *root = cJSON_CreateObject();
    cJSON *devices = cJSON_CreateArray();
    int count = 0;
    {
        std::lock_guard<std::mutex> lk(g_ble_results_mutex);
        count = (int)g_ble_scan_results.size();
        for (const auto &d : g_ble_scan_results)
        {
            cJSON *obj = cJSON_CreateObject();
            cJSON_AddStringToObject(obj, "name", d.name.c_str());
            cJSON_AddStringToObject(obj, "addr", d.addr.c_str());
            cJSON_AddNumberToObject(obj, "rssi", d.rssi);
            cJSON_AddItemToArray(devices, obj);
        }
    }
    cJSON_AddBoolToObject(root, "success", true);
    cJSON_AddNumberToObject(root, "count", count);
    cJSON_AddItemToObject(root, "devices", devices);

    char *json_str = cJSON_PrintUnformatted(root);
    std::string result(json_str ? json_str : "{}");
    if (json_str)
        cJSON_free(json_str);
    cJSON_Delete(root);
    ESP_LOGD("GAP", "BLE scan result: %s", result.c_str());
    return result;
}

// BLE event handling
static int ble_gap_event(struct ble_gap_event *event, void *arg)
{
    struct ble_hs_adv_fields fields;

    switch (event->type)
    {
    // NimBLE event discovery
    case BLE_GAP_EVENT_DISC:
        ESP_LOGI("GAP", "GAP EVENT DISCOVERY");
        ble_hs_adv_parse_fields(&fields, event->disc.data, event->disc.length_data);
        if (fields.name_len > 0)
        {
            printf("Device %d - Name: %.*s\n", scan_device_count + 1, fields.name_len, fields.name);
            printf("=== Advertisement Fields ===\n");
            if (fields.name && fields.name_len > 0)
            {
                printf("Name: %.*s (len: %d)\n", fields.name_len, (char *)fields.name, fields.name_len);
            }
            else
            {
                printf("Name: (null) (len: %d)\n", fields.name_len);
            }
            printf("TX Power: %d\n", fields.tx_pwr_lvl);
            printf("Appearance: 0x%04X\n", fields.appearance);
            printf("Flags: 0x%02X\n", fields.flags);

            // Print UUIDs
            printf("16-bit UUIDs (%d): ", fields.num_uuids16);
            for (int i = 0; i < fields.num_uuids16; i++)
            {
                printf("0x%04X ", ble_uuid_u16(&fields.uuids16[i].u));
            }
            printf("\n");

            printf("32-bit UUIDs (%d): ", fields.num_uuids32);
            for (int i = 0; i < fields.num_uuids32; i++)
            {
                printf("0x%08lX ", (unsigned long)fields.uuids32[i].value);
            }
            printf("\n");

            printf("128-bit UUIDs (%d): ", fields.num_uuids128);
            for (int i = 0; i < fields.num_uuids128; i++)
            {
                printf("0x");
                for (int j = 0; j < 16; j++)
                {
                    printf("%02X", fields.uuids128[i].value[j]);
                }
                printf(" ");
            }
            printf("\n");

            // 检查是否是LINK_PROTOCOL广播包
            if (fields.num_uuids128 > 0 && fields.mfg_data_len >= 7) {
                // 检查制造商数据是否包含LINK协议标识
                if (fields.mfg_data[2] == 'L' && fields.mfg_data[3] == 'I' &&
                    fields.mfg_data[4] == 'N' && fields.mfg_data[5] == 'K') {
                    uint8_t version = fields.mfg_data[6];
                    printf("*** LINK_PROTOCOL Device Detected! ***\n");
                    printf("Protocol Version: %d\n", version);
                    printf("Service UUID: ");
                    for (int j = 0; j < 16; j++) {
                        printf("%02X", fields.uuids128[0].value[j]);
                    }
                    printf("\n");
                    printf("*** End LINK_PROTOCOL Info ***\n");
                }
            }

            // Print manufacturer data
            if (fields.mfg_data_len > 0)
            {
                printf("Manufacturer Data (%d bytes): ", fields.mfg_data_len);
                for (int i = 0; i < fields.mfg_data_len; i++)
                {
                    printf("%02X ", fields.mfg_data[i]);
                }
                printf("\n");
            }
            // record result
            BleDeviceInfo info;
            info.name = std::string(fields.name, fields.name + fields.name_len);
            info.addr = fmt_addr_str(&event->disc.addr);
            info.rssi = event->disc.rssi;
            {
                std::lock_guard<std::mutex> lk(g_ble_results_mutex);
                g_ble_scan_results.push_back(std::move(info));
            }
        }
        else
        {
            printf("Device %d - No name available\n", scan_device_count + 1);
            BleDeviceInfo info;
            info.name = std::string("");
            info.addr = fmt_addr_str(&event->disc.addr);
            info.rssi = event->disc.rssi;
            {
                std::lock_guard<std::mutex> lk(g_ble_results_mutex);
                g_ble_scan_results.push_back(std::move(info));
            }
        }

        scan_device_count++;

        // 如果达到最大扫描设备数量，停止扫描
        if (scan_device_count >= MAX_SCAN_DEVICES)
        {
            printf("Reached maximum scan devices (%d), stopping scan...\n", MAX_SCAN_DEVICES);
            ble_gap_disc_cancel();
        }
        break;

    case BLE_GAP_EVENT_DISC_COMPLETE:
        printf("Scan completed. Total devices found: %d\n", scan_device_count);
        printf("Scan finished.\n");
        if (g_ble_scan_done_sem)
        {
            xSemaphoreGive(g_ble_scan_done_sem);
        }
        break;

    default:
        break;
    }
    return 0;
}

extern "C" {

void ble_app_scan(void)
{
    printf("Start single scan (duration: %d ms, max devices: %d)...\n", SCAN_DURATION_MS, MAX_SCAN_DEVICES);

    // 重置扫描计数器
    scan_device_count = 0;

    struct ble_gap_disc_params disc_params;
    disc_params.filter_duplicates = 1;
    disc_params.passive = 0;
    disc_params.itvl = 0;
    disc_params.window = 0;
    disc_params.filter_policy = 0;
    disc_params.limited = 0;

    // 使用有限时间扫描而不是永久扫描
    ble_gap_disc(ble_addr_type, SCAN_DURATION_MS, &disc_params, ble_gap_event, NULL);
}

// The application
void ble_app_on_sync(void)
{
    ble_hs_id_infer_auto(0, &ble_addr_type); // Determines the best address type automatically
    ble_app_scan();
}

// The infinite task
void host_task(void *param)
{
    nimble_port_run(); // This function will return only when nimble_port_stop() is executed
}

// BLE广播功能实现

// 广播事件处理函数
static int ble_gap_adv_event(struct ble_gap_event *event, void *arg)
{
    switch (event->type) {
    case BLE_GAP_EVENT_ADV_COMPLETE:
        ESP_LOGI("ADV", "Advertising complete; reason=%d", event->adv_complete.reason);
        g_advertising_active = false;
        break;
    default:
        break;
    }
    return 0;
}

// 扩展广播事件处理函数
static int ble_gap_ext_adv_event(struct ble_gap_event *event, void *arg)
{
    switch (event->type) {
    case BLE_GAP_EVENT_ADV_COMPLETE:
        ESP_LOGI("EXT_ADV", "Extended advertising complete; reason=%d", event->adv_complete.reason);
        g_ext_advertising_active = false;
        break;
    default:
        break;
    }
    return 0;
}

// 停止广播
int ble_stop_advertising(void)
{
    if (!g_advertising_active) {
        return 0; // 已经停止
    }

    int rc = ble_gap_adv_stop();
    if (rc == 0) {
        g_advertising_active = false;
        ESP_LOGI("ADV", "Advertising stopped successfully");
    } else {
        ESP_LOGE("ADV", "Failed to stop advertising: %d", rc);
    }
    return rc;
}

// 停止扩展广播（使用常规广播API）
int ble_stop_ext_advertising(void)
{
    if (!g_ext_advertising_active) {
        return 0; // 已经停止
    }

    int rc = ble_gap_adv_stop();
    if (rc == 0) {
        g_ext_advertising_active = false;
        ESP_LOGI("EXT_ADV", "Extended advertising stopped successfully");
    } else {
        ESP_LOGE("EXT_ADV", "Failed to stop extended advertising: %d", rc);
    }
    return rc;
}

// 开始广播 - C接口
int ble_start_advertising(const uint8_t *uuid128, const uint8_t *mfg_data, uint8_t mfg_data_len, const char *device_name)
{
    if (!uuid128 || !mfg_data || !device_name || strlen(device_name) == 0 || mfg_data_len == 0) {
        ESP_LOGE("ADV", "Invalid parameters");
        return -1;
    }

    // 如果已经在广播，先停止
    if (g_advertising_active) {
        ble_stop_advertising();
        vTaskDelay(pdMS_TO_TICKS(100)); // 等待停止完成
    }

    int rc;
    struct ble_hs_adv_fields adv_fields;
    struct ble_gap_adv_params adv_params;

    // 清零结构体
    memset(&adv_fields, 0, sizeof(adv_fields));
    memset(&adv_params, 0, sizeof(adv_params));

    // 设置广播参数
    adv_params.conn_mode = BLE_GAP_CONN_MODE_UND;  // 可连接
    adv_params.disc_mode = BLE_GAP_DISC_MODE_GEN;  // 一般可发现
    adv_params.channel_map = 0;
    adv_params.filter_policy = 0;
    adv_params.high_duty_cycle = 0;

    // 设置广播数据
    adv_fields.flags = BLE_HS_ADV_F_DISC_GEN | BLE_HS_ADV_F_BREDR_UNSUP;

    // 设置设备名称
    adv_fields.name = (uint8_t *)device_name;
    adv_fields.name_len = strlen(device_name);
    adv_fields.name_is_complete = 1;

    // 设置128位服务UUID
    static ble_uuid128_t service_uuid;
    memcpy(service_uuid.value, uuid128, 16);
    service_uuid.u.type = BLE_UUID_TYPE_128;

    adv_fields.uuids128 = &service_uuid;
    adv_fields.num_uuids128 = 1;
    adv_fields.uuids128_is_complete = 1;

    // 设置制造商数据
    adv_fields.mfg_data = mfg_data;
    adv_fields.mfg_data_len = mfg_data_len;

    // 设置广播数据
    rc = ble_gap_adv_set_fields(&adv_fields);
    if (rc != 0) {
        ESP_LOGE("ADV", "Failed to set advertising data: %d", rc);
        return rc;
    }

    // 开始广播
    rc = ble_gap_adv_start(ble_addr_type, NULL, BLE_HS_FOREVER, &adv_params, ble_gap_adv_event, NULL);
    if (rc != 0) {
        ESP_LOGE("ADV", "Failed to start advertising: %d", rc);
        return rc;
    }

    g_advertising_active = true;
    ESP_LOGI("ADV", "Advertising started successfully with device name: %s", device_name);

    return 0;
}

// C++便利函数 - 使用十六进制字符串UUID
int ble_start_advertising_cpp(const std::string& uuid128_hex, const uint8_t *mfg_data, uint8_t mfg_data_len, const std::string& device_name)
{
    if (uuid128_hex.length() != 32) {
        ESP_LOGE("ADV", "UUID must be 32 hex characters (128 bits)");
        return -1;
    }

    if (device_name.empty()) {
        ESP_LOGE("ADV", "Device name cannot be empty");
        return -1;
    }

    // 将十六进制字符串转换为字节数组
    uint8_t uuid128[16];
    for (int i = 0; i < 16; i++) {
        std::string byte_str = uuid128_hex.substr(i * 2, 2);
        uuid128[i] = (uint8_t)strtol(byte_str.c_str(), NULL, 16);
    }

    return ble_start_advertising(uuid128, mfg_data, mfg_data_len, device_name.c_str());
}

// BLE5扩展广播函数 - 包含额外字符串数据
int ble_start_ext_advertising_v1(const uint8_t *uuid128, const uint8_t *mfg_data, uint8_t mfg_data_len, const char *device_name)
{
    if (!uuid128 || !mfg_data || !device_name || strlen(device_name) == 0 || mfg_data_len == 0) {
        ESP_LOGE("EXT_ADV", "Invalid parameters");
        return -1;
    }

    // 如果已经在扩展广播，先停止
    if (g_ext_advertising_active) {
        ble_stop_ext_advertising();
        vTaskDelay(pdMS_TO_TICKS(100)); // 等待停止完成
    }

    int rc;
    struct ble_gap_ext_adv_params ext_adv_params;
    struct ble_hs_adv_fields adv_fields;

    // 清零结构体
    memset(&ext_adv_params, 0, sizeof(ext_adv_params));
    memset(&adv_fields, 0, sizeof(adv_fields));

    // 设置扩展广播参数
    ext_adv_params.connectable = 1;
    ext_adv_params.scannable = 1;
    ext_adv_params.directed = 0;
    ext_adv_params.high_duty_directed = 0;
    ext_adv_params.legacy_pdu = 0;  // 使用扩展PDU
    ext_adv_params.anonymous = 0;
    ext_adv_params.include_tx_power = 0;
    ext_adv_params.scan_req_notif = 0;
    ext_adv_params.itvl_min = BLE_GAP_ADV_FAST_INTERVAL1_MIN;
    ext_adv_params.itvl_max = BLE_GAP_ADV_FAST_INTERVAL1_MAX;
    ext_adv_params.channel_map = 0;
    ext_adv_params.filter_policy = 0;
    ext_adv_params.primary_phy = BLE_HCI_LE_PHY_1M;
    ext_adv_params.secondary_phy = BLE_HCI_LE_PHY_1M;
    ext_adv_params.tx_power = 127;  // 使用默认功率
    ext_adv_params.sid = 0;

    // 配置扩展广播实例
    rc = ble_gap_ext_adv_configure(g_ext_adv_instance, &ext_adv_params, NULL, ble_gap_ext_adv_event, NULL);
    if (rc != 0) {
        ESP_LOGE("EXT_ADV", "Failed to configure extended advertising: %d", rc);
        return rc;
    }

    // 设置主广播数据
    adv_fields.flags = BLE_HS_ADV_F_DISC_GEN | BLE_HS_ADV_F_BREDR_UNSUP;

    // 设置设备名称
    adv_fields.name = (uint8_t *)device_name;
    adv_fields.name_len = strlen(device_name);
    adv_fields.name_is_complete = 1;

    // 设置128位服务UUID
    static ble_uuid128_t service_uuid;
    memcpy(service_uuid.value, uuid128, 16);
    service_uuid.u.type = BLE_UUID_TYPE_128;

    adv_fields.uuids128 = &service_uuid;
    adv_fields.num_uuids128 = 1;
    adv_fields.uuids128_is_complete = 1;

    // 设置制造商数据
    adv_fields.mfg_data = mfg_data;
    adv_fields.mfg_data_len = mfg_data_len;

    // 设置主广播数据
    rc = ble_gap_ext_adv_set_data(g_ext_adv_instance, &adv_fields);
    if (rc != 0) {
        ESP_LOGE("EXT_ADV", "Failed to set extended advertising data: %d", rc);
        return rc;
    }

    return 0;
}
