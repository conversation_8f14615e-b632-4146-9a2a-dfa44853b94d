# BLE5扩展广播功能使用指南

## 功能概述

本项目现在支持BLE5扩展广播功能，可以在扫描响应中包含额外的数据。这个功能特别适用于需要传输较长字符串或额外信息的场景。

## 新增API函数

### C接口

```c
// BLE5扩展广播 - 支持额外数据在扫描响应中
int ble_start_ext_advertising_with_scan_rsp(const uint8_t *uuid128, 
                                            const uint8_t *mfg_data, 
                                            uint8_t mfg_data_len, 
                                            const char *device_name, 
                                            const char *extra_data);
```

### 参数说明

- `uuid128`: 128位UUID（16字节数组）
- `mfg_data`: 制造商数据（在主广播包中）
- `mfg_data_len`: 制造商数据长度
- `device_name`: 设备名称（在主广播包中）
- `extra_data`: 额外数据字符串（在扫描响应包中）

## 广播数据结构

### 主广播包内容
- 标志（Flags）
- 设备名称（Complete Local Name）
- 128位服务UUID
- 制造商数据

### 扫描响应包内容
- 制造商数据（包含额外字符串）
  - 制造商ID: 0xFFFF
  - 额外数据: 您指定的字符串

## 使用示例

### 示例1：基本使用

```c
#include "link_protocol.h"

void start_ble5_advertising_example() {
    // 定义128位UUID
    uint8_t my_uuid[16] = {
        0x12, 0x34, 0x56, 0x78, 0x12, 0x34, 0x56, 0x78,
        0x9A, 0xBC, 0xDE, 0xF0, 0x12, 0x34, 0x56, 0x78
    };
    
    // 定义制造商数据
    uint8_t mfg_data[] = {
        0xFF, 0xFF,           // 制造商ID
        'L', 'I', 'N', 'K',   // 协议标识
        0x01                  // 版本号
    };
    
    const char* device_name = "BLE5-Device";
    const char* extra_data = "QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT";
    
    // 开始扩展广播
    int result = ble_start_ext_advertising_with_scan_rsp(
        my_uuid, 
        mfg_data, 
        sizeof(mfg_data), 
        device_name, 
        extra_data
    );
    
    if (result == 0) {
        printf("BLE5扩展广播启动成功\n");
    } else {
        printf("BLE5扩展广播启动失败: %d\n", result);
    }
}
```

### 示例2：指定的额外数据

```c
void start_advertising_with_specified_string() {
    uint8_t uuid[16] = {0x12, 0x34, 0x56, 0x78, 0x12, 0x34, 0x56, 0x78,
                        0x9A, 0xBC, 0xDE, 0xF0, 0x12, 0x34, 0x56, 0x78};
    
    uint8_t mfg_data[] = {0xFF, 0xFF, 'L', 'I', 'N', 'K', 0x01};
    
    // 使用指定的字符串作为额外数据
    const char* extra_string = "QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT";
    
    int result = ble_start_ext_advertising_with_scan_rsp(
        uuid, 
        mfg_data, 
        sizeof(mfg_data), 
        "MyDevice", 
        extra_string
    );
    
    if (result == 0) {
        printf("广播已启动，额外数据: %s\n", extra_string);
    }
}
```

## 扫描和检测

扫描功能已经能够检测和显示：
- 128位UUID
- 制造商数据
- 扫描响应中的额外数据

当扫描到包含指定字符串的设备时，会在控制台输出相关信息。

## 技术细节

### BLE5扩展广播优势
1. **更大的数据容量**: 支持更长的广播数据
2. **扫描响应**: 可以在扫描响应中包含额外信息
3. **向后兼容**: 与传统BLE设备兼容

### 数据限制
- 主广播包: 最大31字节（传统模式）或251字节（扩展模式）
- 扫描响应包: 最大31字节（传统模式）或251字节（扩展模式）
- 额外数据字符串: 最大251字节（减去制造商ID的2字节）

### 错误代码
- `0`: 成功
- `-1`: 参数无效
- 其他: NimBLE错误代码

## 注意事项

1. **字符串长度**: 额外数据字符串不能超过251字节
2. **设备名称**: 不能为空
3. **UUID格式**: 必须是16字节的二进制数据
4. **广播冲突**: 如果已经在广播，新的广播会先停止旧的
5. **扫描响应**: 只有在主动扫描时才能接收到扫描响应数据

## 停止广播

使用相同的停止函数：

```c
int result = ble_stop_advertising();
if (result == 0) {
    printf("广播已停止\n");
}
```

这个功能使您能够在BLE广播中包含更多信息，特别适合需要传输较长标识符或数据的应用场景。
