#ifndef LINK_PROTOCOL_H
#define LINK_PROTOCOL_H
#include <stdio.h>
#include <vector>
#include <string>
#include <mutex>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_event.h"
#include "nvs_flash.h"
#include "esp_log.h"
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "services/gap/ble_svc_gap.h"
#include "sdkconfig.h"
#include <cJSON.h>
#include <string>
//LINK PROTOCOL CONSTANTS
#define LINK_PROTOCOL_MANUFACTURER_ID 0xFFFF

#ifdef __cplusplus
extern "C" {
#endif

// C-compatible Bluetooth scanning function declarations
void ble_app_scan(void);
void ble_app_on_sync(void);
void host_task(void *param);

// C-compatible Bluetooth advertising function declarations
int ble_start_advertising(const uint8_t *uuid128, const uint8_t *mfg_data, uint8_t mfg_data_len, const char *device_name);
int ble_stop_advertising(void);

#if CONFIG_BT_NIMBLE_EXT_ADV
// BLE5 Extended advertising function declarations
int ble_start_ext_advertising_v1(const uint8_t *uuid128, const char *data_string, const char *device_name);
int ble_stop_ext_advertising(void);
#endif

#ifdef __cplusplus
}

// C++ function declarations (cannot be in extern "C" block)
std::string ble_scan_start_and_wait_json();

// C++ advertising convenience functions
int ble_start_advertising_cpp(const std::string& uuid128_hex, const uint8_t *mfg_data, uint8_t mfg_data_len, const std::string& device_name);

#endif

#endif // LINK_PROTOCOL_H
