/*
 * BLE广播功能使用示例
 * 
 * 此示例展示如何使用ble_start_advertising函数进行BLE广播
 * 参数：128位UUID + 自定义制造商数据
 */

#include "link_protocol.h"
#include <string.h>

void example_start_advertising() {
    // 定义128位UUID (16字节)
    uint8_t my_uuid[16] = {
        0x12, 0x34, 0x56, 0x78, 0x12, 0x34, 0x56, 0x78,
        0x9A, 0xBC, 0xDE, 0xF0, 0x12, 0x34, 0x56, 0x78
    };
    
    // 定义制造商数据
    uint8_t mfg_data[] = {
        0xFF, 0xFF,           // 制造商ID (0xFFFF)
        'L', 'I', 'N', 'K',   // 协议标识 "LINK"
        0x01                  // 版本号
    };
    
    const char* device_name = "MyBLEDevice";
    
    // 开始广播
    int result = ble_start_advertising(my_uuid, mfg_data, sizeof(mfg_data), device_name);
    
    if (result == 0) {
        printf("广播启动成功\n");
    } else {
        printf("广播启动失败: %d\n", result);
    }
}

void example_stop_advertising() {
    // 停止广播
    int result = ble_stop_advertising();
    
    if (result == 0) {
        printf("广播停止成功\n");
    } else {
        printf("广播停止失败: %d\n", result);
    }
}

#ifdef __cplusplus
void example_start_advertising_cpp() {
    // 使用C++接口的示例
    std::string uuid = "123456781234567889ABCDEF01234567"; // 32个十六进制字符
    std::string device_name = "MyCppDevice";

    // 定义制造商数据
    uint8_t mfg_data[] = {
        0xFF, 0xFF,           // 制造商ID
        'L', 'I', 'N', 'K',   // 协议标识
        0x02                  // 版本号
    };

    int result = ble_start_advertising_cpp(uuid, mfg_data, sizeof(mfg_data), device_name);

    if (result == 0) {
        printf("C++广播启动成功\n");
    } else {
        printf("C++广播启动失败: %d\n", result);
    }
}

void example_start_ext_advertising_with_extra_data() {
    // 使用BLE5扩展广播功能，包含额外数据
    uint8_t my_uuid[16] = {
        0x12, 0x34, 0x56, 0x78, 0x12, 0x34, 0x56, 0x78,
        0x9A, 0xBC, 0xDE, 0xF0, 0x12, 0x34, 0x56, 0x78
    };

    // 定义制造商数据
    uint8_t mfg_data[] = {
        0xFF, 0xFF,           // 制造商ID
        'L', 'I', 'N', 'K',   // 协议标识
        0x01                  // 版本号
    };

    const char* device_name = "BLE5-ExtAdv-Device";
    const char* extra_data = "QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT"; // 指定的额外广播字符串

    // 开始扩展广播，额外数据将在扫描响应中发送
    int result = ble_start_ext_advertising_with_scan_rsp(my_uuid, mfg_data, sizeof(mfg_data), device_name, extra_data);

    if (result == 0) {
        printf("BLE5扩展广播启动成功\n");
        printf("设备名称: %s\n", device_name);
        printf("额外数据: %s\n", extra_data);
    } else {
        printf("BLE5扩展广播启动失败: %d\n", result);
    }
}
#endif
