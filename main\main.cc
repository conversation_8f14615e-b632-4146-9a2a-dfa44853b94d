#include <esp_log.h>
#include <esp_err.h>
#include <nvs.h>
#include <nvs_flash.h>
#include <driver/gpio.h>
#include "link_protocol.h"

static const char* TAG = "MAIN";

uint8_t ble_addr_type;

#if CONFIG_BT_NIMBLE_EXT_ADV
// 测试任务：演示停止和重新启动扩展广播
void ext_adv_test_task(void *param)
{
    vTaskDelay(pdMS_TO_TICKS(10000)); // 等待10秒

    ESP_LOGI(TAG, "=== Extended Advertising Test ===");
    ESP_LOGI(TAG, "Stopping extended advertising...");

    int result = ble_stop_ext_advertising();
    if (result == 0) {
        ESP_LOGI(TAG, "✓ Extended advertising stopped");
    } else {
        ESP_LOGE(TAG, "✗ Failed to stop extended advertising: %d", result);
    }

    vTaskDelay(pdMS_TO_TICKS(5000)); // 等待5秒

    ESP_LOGI(TAG, "Restarting extended advertising with different data...");

    // 使用不同的数据重新启动
    uint8_t test_uuid2[16] = {
        0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0,
        0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88
    };

    const char* test_data = "QmTestHashForBLE5ExtendedAdvertising123456789";
    const char* test_device_name = "BLE5-Test-Node";

    result = ble_start_ext_advertising_v1(test_uuid2, test_data, test_device_name);
    if (result == 0) {
        ESP_LOGI(TAG, "✓ Extended advertising restarted with new data");
        ESP_LOGI(TAG, "New data: %s", test_data);
    } else {
        ESP_LOGE(TAG, "✗ Failed to restart extended advertising: %d", result);
    }

    // 任务完成，删除自己
    vTaskDelete(NULL);
}
#endif

// Test function to demonstrate advertising
extern "C" void app_main(void)
{
    // Initialize the default event loop
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    // Initialize NVS flash for WiFi configuration
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_LOGW(TAG, "Erasing NVS flash to fix corruption");
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    nimble_port_init();                             // 2 - Initialize the host and controller stack
    ble_svc_gap_device_name_set("BLE-Scan-Client"); // 3 - Set device name characteristic
    ble_svc_gap_init();                             // 3 - Initialize GAP service

    // Set a custom sync callback that will schedule extended advertising
    ble_hs_cfg.sync_cb = [](void) {
        ble_hs_id_infer_auto(0, &ble_addr_type);
        ESP_LOGI(TAG, "BLE Host synchronized, scheduling extended advertising...");

#if CONFIG_BT_NIMBLE_EXT_ADV
        // 创建一个任务来启动扩展广播，避免在同步回调中直接调用
        xTaskCreate([](void* param) {
            vTaskDelay(pdMS_TO_TICKS(100)); // 等待同步完成

            ESP_LOGI(TAG, "Starting BLE5 Extended Advertising test...");

            // 定义128位UUID
            uint8_t test_uuid[16] = {
                0x6E, 0x40, 0x00, 0x01, 0xB5, 0xA3, 0xF3, 0x93,
                0xE0, 0xA9, 0xE5, 0x0E, 0x24, 0xDC, 0xCA, 0x9E
            };

            // 要广播的字符串
            const char* ipfs_hash = "QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT";
            const char* device_name = "IPFS-BLE5-Node";

            ESP_LOGI(TAG, "Broadcasting IPFS hash: %s", ipfs_hash);
            ESP_LOGI(TAG, "String length: %zu bytes", strlen(ipfs_hash));
            ESP_LOGI(TAG, "Device name: %s", device_name);

            // 启动扩展广播
            int result = ble_start_ext_advertising_v1(test_uuid, ipfs_hash, device_name);

            if (result == 0) {
                ESP_LOGI(TAG, "✓ BLE5 Extended Advertising started successfully!");
                ESP_LOGI(TAG, "  - Using BLE5 Extended Advertising PDU");
                ESP_LOGI(TAG, "  - Data in manufacturer field (ID: 0xFFFF)");
                ESP_LOGI(TAG, "  - Continuous advertising mode");

                // 启动测试任务
                xTaskCreate(ext_adv_test_task, "ext_adv_test", 4096, NULL, 5, NULL);
                ESP_LOGI(TAG, "Started extended advertising test task");
            } else {
                ESP_LOGE(TAG, "✗ Failed to start BLE5 Extended Advertising: %d", result);
            }

            // 删除这个临时任务
            vTaskDelete(NULL);
        }, "start_ext_adv", 4096, NULL, 5, NULL);
#else
        ESP_LOGW(TAG, "BLE5 Extended Advertising not supported - CONFIG_BT_NIMBLE_EXT_ADV not enabled");
        ESP_LOGI(TAG, "Starting regular scan instead...");
        // 如果不支持扩展广播，则启动扫描
        xTaskCreate([](void* param) {
            vTaskDelay(pdMS_TO_TICKS(100));
            ble_app_scan();
            vTaskDelete(NULL);
        }, "start_scan", 2048, NULL, 5, NULL);
#endif
    };

    nimble_port_freertos_init(host_task);           // 5 - Set infinite task
}
